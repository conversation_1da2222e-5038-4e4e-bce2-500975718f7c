from aiogram import types, Router
from aiogram.filters import Command
from db import list_all_users

router = Router()

# 👇 Здесь укажи свой Telegram ID
ADMIN_IDS = [123456789]  # ← замени на свой ID

@router.message(Command("adminpanel"))
async def admin_panel(message: types.Message):
    if message.from_user.id not in ADMIN_IDS:
        await message.answer("❌ У вас нет доступа к админ-панели.")
        return

    users = list_all_users()
    if not users:
        await message.answer("⛔️ Пока никто не зарегистрирован.")
    else:
        text = "\n\n".join(
            f"👤 {u['first_name'] or ''} (@{u['username'] or '—'})\n🆔 {u['telegram_id']}\n📁 Папка: {u['drive_folder_id']}"
            for u in users
        )
        await message.answer(f"📋 Зарегистрированные пользователи:\n\n{text}")
