import openai
import json

with open("config.json", encoding="utf-8") as f:
    config = json.load(f)

openai.api_key = config["openai_api_key"]

def classify_note(user_input):
    system_prompt = (
        "Ты — помощник по организации заметок.\n"
        "Проанализируй текст и предложи краткое имя папки (1 слово на русском), "
        "куда следует сохранить эту заметку в Obsidian.\n"
        "Используй категории: философия, тело, мышление, цели, творчество, дневник, отношения, проекты, идеи.\n"
        "Если не уверен — ответь: Unsorted.\n"
    )

    response = openai.chat.completions.create(
        model="gpt-4",
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_input}
        ],
        temperature=0.3
    )

    folder = response.choices[0].message.content.strip()
    return folder
import openai
import json

with open("config.json", encoding="utf-8") as f:
    config = json.load(f)

openai.api_key = config["openai_api_key"]

def classify_note(user_input):
    system_prompt = (
        "Ты — помощник по организации заметок.\n"
        "Проанализируй текст и предложи краткое имя папки (1 слово на русском), "
        "куда следует сохранить эту заметку в Obsidian.\n"
        "Используй категории: философия, тело, мышление, цели, творчество, дневник, отношения, проекты, идеи.\n"
        "Если не уверен — ответь: Unsorted.\n"
    )

    response = openai.chat.completions.create(
        model="gpt-4o",
      

        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_input}
        ],
        temperature=0.3
    )

    folder = response.choices[0].message.content.strip()
    return folder
