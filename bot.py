import asyncio
import json
import logging
import sqlite3
from aiogram import <PERSON><PERSON>, Di<PERSON>atcher, types
from aiogram.filters import Command
from utils.md_writer import save_to_markdown
from utils.classifier import classify_note
from gdrive.drive_uploader import upload_to_drive
from fileadmin import router as admin_router

# Настройка логирования
logging.basicConfig(level=logging.INFO, filename="bot.log")
logger = logging.getLogger(__name__)

# Константы
MESSAGES = {
    "ALREADY_REGISTERED": "✅ Уже зарегистрирован.",
    "SUCCESS_REGISTER": "✅ Вы успешно зарегистрированы. Можно писать заметки!",
    "NOT_REGISTERED": "❌ Пожалуйста, введите /start для регистрации.",
    "INVALID_INPUT": "❌ Заметка пустая или слишком длинная.",
    "SAVE_ERROR": "❌ Ошибка при сохранении заметки."
}

# Инициализация базы данных
def init_db():
    conn = sqlite3.connect("users.db")
    cursor = conn.cursor()
    cursor.execute("CREATE TABLE IF NOT EXISTS users (user_id INTEGER PRIMARY KEY)")
    conn.commit()
    conn.close()

def is_registered(user_id: int) -> bool:
    conn = sqlite3.connect("users.db")
    cursor = conn.cursor()
    cursor.execute("SELECT user_id FROM users WHERE user_id = ?", (user_id,))
    result = cursor.fetchone()
    conn.close()
    return result is not None

def register_user(user_id: int):
    conn = sqlite3.connect("users.db")
    cursor = conn.cursor()
    cursor.execute("INSERT OR IGNORE INTO users (user_id) VALUES (?)", (user_id,))
    conn.commit()
    conn.close()

# Загрузка конфигурации
try:
    with open("config.json", encoding="utf-8") as f:
        config = json.load(f)
except (FileNotFoundError, json.JSONDecodeError) as e:
    logger.error(f"Config loading error: {e}")
    raise

bot = Bot(token=config["telegram_token"])
dp = Dispatcher()
dp.include_router(admin_router)

@dp.message(Command("start"))
async def handle_start(message: types.Message) -> None:
    user_id = message.from_user.id
    if is_registered(user_id):
        await message.answer(MESSAGES["ALREADY_REGISTERED"])
    else:
        register_user(user_id)
        await message.answer(MESSAGES["SUCCESS_REGISTER"])

@dp.message()
async def handle_message(message: types.Message) -> None:
    user_id = message.from_user.id
    if not is_registered(user_id):
        await message.answer(MESSAGES["NOT_REGISTERED"])
        return

    user_input = message.text.strip()
    if not user_input or len(user_input) > 1000:
        await message.answer(MESSAGES["INVALID_INPUT"])
        return

    try:
        category = classify_note(user_input)
        filename, content = save_to_markdown(prompt=user_input, response="Ответ будет здесь", category=category)
        file_url = upload_to_drive(filename, content, category)
        await message.answer(
            f"✅ Заметка сохранена в категорию **{category}**.\n📄 [Ссылка на файл]({file_url})",
            parse_mode="Markdown"
        )
    except Exception as e:
        logger.error(f"Error processing note: {e}")
        await message.answer(MESSAGES["SAVE_ERROR"])

async def main():
    init_db()
    await bot.delete_webhook(drop_pending_updates=True)
    await dp.start_polling(bot)

if __name__ == "__main__":
    asyncio.run(main())