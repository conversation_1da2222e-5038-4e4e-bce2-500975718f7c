from pydrive2.auth import GoogleAuth
from pydrive2.drive import GoogleDrive

gauth = GoogleAuth()
gauth.LocalWebserverAuth()
drive = GoogleDrive(gauth)

def get_or_create_obsidian_root():
    folder_list = drive.ListFile({
        'q': "title='ObsidianNotes' and mimeType='application/vnd.google-apps.folder' and trashed=false"
    }).GetList()
    if folder_list:
        return folder_list[0]
    else:
        folder = drive.CreateFile({'title': 'ObsidianNotes', 'mimeType': 'application/vnd.google-apps.folder'})
        folder.Upload()
        return folder

def create_user_folder(user_id):
    obsidian_root = get_or_create_obsidian_root()
    folder_title = f'User_{user_id}'

    existing = drive.ListFile({
        'q': f"'{obsidian_root['id']}' in parents and title='{folder_title}' and mimeType='application/vnd.google-apps.folder' and trashed=false"
    }).GetList()

    if existing:
        return existing[0]['id']
    else:
        folder = drive.CreateFile({
            'title': folder_title,
            'parents': [{'id': obsidian_root['id']}],
            'mimeType': 'application/vnd.google-apps.folder'
        })
        folder.Upload()
        return folder['id']

def upload_to_drive(filename, content, category, user_folder_id):
    # Найти или создать папку категории в папке пользователя
    sub_folders = drive.ListFile({
        'q': f"'{user_folder_id}' in parents and title='{category}' and mimeType='application/vnd.google-apps.folder' and trashed=false"
    }).GetList()

    if sub_folders:
        category_folder = sub_folders[0]
    else:
        category_folder = drive.CreateFile({
            'title': category,
            'parents': [{'id': user_folder_id}],
            'mimeType': 'application/vnd.google-apps.folder'
        })
        category_folder.Upload()

    file_drive = drive.CreateFile({
        'title': filename,
        'parents': [{'id': category_folder['id']}]
    })
    file_drive.SetContentString(content)
    file_drive.Upload()

    return file_drive['alternateLink']
